<template>
  <div :class="['input-field flex flex-col gap-1 w-full', $attrs.class]">
    <label
      v-if="label"
      class="block text-body-3 font-medium text-neutral-700 mb-2 transition-colors"
      :class="{ 'text-primary-600': isFocused }"
    >
      {{ label }}
      <span v-if="required && showRequired" class="text-primary-200 text-body-5 ml-1.5">⁕</span>
    </label>

    <div
      class="relative flex items-center w-full"
      @focusin="isFocused = true"
      @focusout="isFocused = false"
    >
      <slot name="left-slot" />
      <input
        :type="type"
        :required="required"
        :value="modelValue"
        :maxlength="maxlength"
        :placeholder="placeholder"
        :disabled="disabled"
        @input="onInput"
        class="w-full px-4 py-4 bg-neutral-50 border border-neutral-200 rounded-xl text-body-2 placeholder-neutral-400 transition-all duration-300"
        :class="{ 'border-primary-500 bg-white focus:ring-4 focus:ring-primary-100': isFocused }"
      />
      <div class="absolute right-4">
        <slot name="icon" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = withDefaults(defineProps<{
  modelValue: string
  label?: string
  type?: string
  showRequired?: boolean
  required?: boolean
  maxlength?: number
  placeholder?: string
  disabled?: boolean
}>(), {
  type: 'text',
  required: true,
  showRequired: true,
  maxlength: undefined,
  placeholder: '',
  disabled: false,
  label: ''
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const isFocused = ref(false)

function onInput(event: Event) {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
}
</script>
