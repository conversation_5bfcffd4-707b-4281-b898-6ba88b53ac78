<template>
  <div class="p-4 sm:p-6">
    <!-- Header with Toggle and Add Button -->
    <div class="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0 mb-6 sm:mb-8">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold text-neutral-900">{{ showEvents ? 'Events' : 'Services' }}</h1>
        <p class="text-neutral-600 mt-1 text-sm sm:text-base">{{ showEvents ? 'Manage your events and activities' : 'Manage your services' }}</p>
      </div>
      <div class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
        <div class="flex bg-neutral-100 rounded-lg p-1 w-full sm:w-auto">
          <button @click="showEvents = false" :class="toggleButtonClass(false)" class="flex-1 sm:flex-none">
            <Icon name="lucide:briefcase" class="w-4 h-4" />
            <span class="text-xs sm:text-sm">Services</span>
          </button>
          <button @click="showEvents = true" :class="toggleButtonClass(true)" class="flex-1 sm:flex-none">
            <Icon name="lucide:calendar" class="w-4 h-4" />
            <span class="text-xs sm:text-sm">Events</span>
          </button>
        </div>
        <button @click="handleAddClick" class="inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 w-full sm:w-auto">
          <Icon name="lucide:plus" class="w-4 h-4 mr-2" />
          <span class="text-sm sm:text-base">{{ showEvents ? 'Add Event' : 'Add Service' }}</span>
        </button>
      </div>
    </div>

    <!-- Search -->
    <div class="mb-6">
      <div class="relative w-full sm:max-w-md">
        <Icon name="lucide:search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
        <input v-model="searchQuery" type="text" :placeholder="`Search ${showEvents ? 'events' : 'services'}...`"
                class="w-full pl-10 pr-4 py-3 bg-white border border-neutral-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm sm:text-base" />
      </div>
    </div>

    <!-- Content Grid -->
    <div v-if="currentItems.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
      <ServiceCard v-if="!showEvents" v-for="service in currentItems as Service[]" :key="service.id" :service="service" @edit="editItem" @delete="deleteItem" />
      <EventCard v-else v-for="event in currentItems as Event[]" :key="event.id" :event="event" @edit="editItem" @delete="deleteItem" />
    </div>

    <!-- Empty States -->
    <div v-else class="text-center py-12">
      <Icon :name="emptyStateIcon" class="w-16 h-16 text-neutral-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-neutral-900 mb-2">{{ emptyStateTitle }}</h3>
      <p class="text-neutral-600">{{ emptyStateMessage }}</p>
    </div>

    <!-- Modals -->
    <ServiceModal v-if="!showEvents && (showAddModal || editingItem)" :service="editingItem && !showEvents ? editingItem as Service : null" @close="closeModal" @save="handleSave" />
    <EventModal v-if="showEvents && (showAddModal || editingItem)" :event="editingItem && showEvents ? editingItem as Event : null" @close="closeModal" @save="handleSave" />

    <!-- Delete Modal -->
    <DeleteModal
      :is-open="showDeleteModal"
      :item-type="showEvents ? 'event' : 'service'"
      :item-name="getItemName(deletingItem)"
      :item-description="deletingItem?.description || ''"
      :item-price="deletingItem?.price"
      :item-duration="getItemDuration(deletingItem)"
      :item-date="getItemDate(deletingItem)"
      :loading="deleteLoading"
      @close="closeDeleteModal"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script setup lang="ts">
import type { Service, ServiceFormData, Event, EventFormData } from '~/types/service.types'
import { $toast } from '~/composables/useToast'
import { serviceApi, eventApi, filterServices, filterEvents, handleApiError } from '~/utils/services'
import ServiceModal from '~/components/services/ServiceModal.vue'
import ServiceCard from '~/components/services/ServiceCard.vue'
import EventModal from '~/components/events/EventModal.vue'
import EventCard from '~/components/events/EventCard.vue'
import DeleteModal from '~/components/ui/DeleteModal.vue'

definePageMeta({ layout: 'dashboard' })
useHead({
  title: 'Services & Events - Bookiime',
  meta: [{ name: 'description', content: 'Manage your services, events and offerings' }]
})

// State
const services = ref<Service[]>([])
const events = ref<Event[]>([])
const loading = ref(false)
const showEvents = ref(false)
const searchQuery = ref('')
const showAddModal = ref(false)
const editingItem = ref<Service | Event | null>(null)
const showDeleteModal = ref(false)
const deletingItem = ref<Service | Event | null>(null)
const deleteLoading = ref(false)

// Computed properties
const currentItems = computed(() => showEvents.value ? filterEvents(events.value, searchQuery.value) : filterServices(services.value, searchQuery.value))
const emptyStateIcon = computed(() => showEvents.value ? 'lucide:calendar' : 'lucide:package')
const emptyStateTitle = computed(() => currentItems.value.length === 0 && searchQuery.value ? `No ${showEvents.value ? 'events' : 'services'} match your search` : `No ${showEvents.value ? 'events' : 'services'} found`)
const emptyStateMessage = computed(() => currentItems.value.length === 0 && searchQuery.value ? 'Try adjusting your search terms.' : `Get started by adding your first ${showEvents.value ? 'event' : 'service'}.`)



// Methods
const toggleButtonClass = (isEvents: boolean) => [
  'flex items-center justify-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 rounded-md text-xs sm:text-sm font-medium transition-all duration-200',
  showEvents.value === isEvents ? 'bg-white text-primary-600 shadow-sm' : 'text-neutral-600 hover:text-neutral-900'
]

const loadData = async () => {
  loading.value = true
  try {
    const [servicesData] = await Promise.all([
      serviceApi.getTenant(),
      eventApi.getAll().then(data => events.value = data)
    ])
    services.value = servicesData
  } catch (err) {
    $toast(handleApiError(err, 'Failed to load data'), { type: 'error' })
  } finally {
    loading.value = false
  }
}

const editItem = (item: Service | Event) => {
  editingItem.value = { ...item }
}

const deleteItem = (item: Service | Event) => {
  deletingItem.value = item
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  if (!deletingItem.value) return

  deleteLoading.value = true
  try {
    if (showEvents.value) {
      await eventApi.delete(deletingItem.value.id)
      events.value = events.value.filter(e => e.id !== deletingItem.value!.id)
    } else {
      await serviceApi.delete(deletingItem.value.id)
      services.value = services.value.filter(s => s.id !== deletingItem.value!.id)
    }
    $toast(`${showEvents.value ? 'Event' : 'Service'} deleted successfully`, { type: 'success' })
    closeDeleteModal()
  } catch (err) {
    $toast(handleApiError(err, `Failed to delete ${showEvents.value ? 'event' : 'service'}`), { type: 'error' })
  } finally {
    deleteLoading.value = false
  }
}

const closeDeleteModal = () => {
  showDeleteModal.value = false
  deletingItem.value = null
}

// Helper functions for delete modal
const getItemName = (item: Service | Event | null): string => {
  if (!item) return ''
  return 'title' in item ? item.title : item.name
}

const getItemDuration = (item: Service | Event | null): number | undefined => {
  if (!item || !('duration' in item)) return undefined
  return item.duration
}

const getItemDate = (item: Service | Event | null): string | undefined => {
  if (!item || !('startDate' in item)) return undefined
  return formatEventDate(item.startDate, item.endDate)
}

// Event date formatting helper
const formatEventDate = (startDate: string, endDate?: string): string => {
  const start = new Date(startDate)
  const end = endDate ? new Date(endDate) : null

  const options: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    year: start.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
  }

  if (end && start.toDateString() !== end.toDateString()) {
    return `${start.toLocaleDateString('en-US', options)} - ${end.toLocaleDateString('en-US', options)}`
  }

  return start.toLocaleDateString('en-US', options)
}

const handleSave = async (data: ServiceFormData | EventFormData, imageFile?: File | null) => {
  try {
    if (showEvents.value) {
      const eventData = data as EventFormData
      if (editingItem.value) {
        await eventApi.update(editingItem.value.id, eventData, imageFile || undefined)
        const index = events.value.findIndex(e => e.id === editingItem.value!.id)
        if (index !== -1) events.value[index] = { ...editingItem.value, ...eventData } as Event
      } else {
        const newEvent = await eventApi.create(eventData, imageFile || undefined)
        events.value.push(newEvent)
      }
    } else {
      const serviceData = data as ServiceFormData
      if (editingItem.value) {
        const updated = await serviceApi.update(editingItem.value.id, serviceData, imageFile || undefined)
        const index = services.value.findIndex(s => s.id === editingItem.value!.id)
        if (index !== -1) services.value[index] = updated
      } else {
        const newService = await serviceApi.create(serviceData, imageFile || undefined)
        services.value.push(newService)
      }
    }
    $toast(`${showEvents.value ? 'Event' : 'Service'} ${editingItem.value ? 'updated' : 'created'} successfully`, { type: 'success' })
    closeModal()
  } catch (err) {
    $toast(handleApiError(err, `Failed to save ${showEvents.value ? 'event' : 'service'}`), { type: 'error' })
  }
}

const handleAddClick = () => {
  showAddModal.value = true
  editingItem.value = null
}

const closeModal = () => {
  showAddModal.value = false
  editingItem.value = null
}

onMounted(loadData)

</script>