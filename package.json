{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/fonts": "^0.11.1", "@nuxt/icon": "^1.15.0", "@tailwindcss/vite": "^4.1.11", "@vueuse/core": "^13.7.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.525.0", "nuxt": "^3.17.6", "nuxt-icon": "^1.0.0-beta.7", "reka-ui": "^2.4.1", "shadcn-nuxt": "^2.0.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "vue": "^3.5.17", "vue-router": "^4.5.1"}}