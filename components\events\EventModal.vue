<template>
  <Teleport to="body">
    <div class="fixed inset-0 z-[9999] overflow-y-auto">
      <!-- Backdrop -->
      <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="flex min-h-full items-center justify-center p-4">
        <div class="relative bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-neutral-200">
          <h2 class="text-xl font-semibold text-neutral-900">
            {{ event ? 'Edit Event' : 'Add New Event' }}
          </h2>
          <Button
            @click="$emit('close')"
            variant="ghost"
            size="sm"
            class="text-neutral-400 hover:text-neutral-600"
          >
            <Icon name="lucide:x" class="w-5 h-5" />
          </Button>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
          <!-- Image Upload -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">Event Image</label>
            <ImageUpload
              v-model="form.imageUrl"
              @upload="handleImageUpload"
              :loading="imageUploading"
            />
          </div>

          <!-- Event Title -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">
              Event Title <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter event title"
            />
          </div>

          <!-- Event Description -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">
              Description <span class="text-red-500">*</span>
            </label>
            <textarea
              v-model="form.description"
              required
              rows="3"
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Describe your event"
            />
          </div>

          <!-- Date and Time -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">
                Start Date <span class="text-red-500">*</span>
              </label>
              <input
                v-model="form.startDate"
                type="date"
                required
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">
                End Date <span class="text-red-500">*</span>
              </label>
              <input
                v-model="form.endDate"
                type="date"
                required
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">
                Start Time <span class="text-red-500">*</span>
              </label>
              <input
                v-model="form.startTime"
                type="time"
                required
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">
                End Time <span class="text-red-500">*</span>
              </label>
              <input
                v-model="form.endTime"
                type="time"
                required
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <!-- Location and Category -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">Location</label>
              <input
                v-model="form.location"
                type="text"
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Event location"
              />
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">Category</label>
              <input
                v-model="form.category"
                type="text"
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="e.g., Workshop, Seminar, Training"
              />
            </div>
          </div>

          <!-- Price and Max Attendees -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">Price (₵)</label>
              <input
                v-model.number="form.price"
                type="number"
                min="0"
                step="0.01"
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="0.00"
              />
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">Max Attendees</label>
              <input
                v-model.number="form.maxAttendees"
                type="number"
                min="1"
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Unlimited"
              />
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
            <Button
              type="button"
              @click="$emit('close')"
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              :disabled="loading"
              class="bg-primary-600 text-white hover:bg-primary-700"
            >
              <Icon v-if="loading" name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
              {{ event ? 'Update Event' : 'Create Event' }}
            </Button>
          </div>
        </form>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import type { Event, EventFormData } from '~/types/service.types'
import { Button } from '@/components/ui/button'
import ImageUpload from '@/components/services/ImageUpload.vue'

interface Props {
  event?: Event | null
}

interface Emits {
  (e: 'close'): void
  (e: 'save', data: EventFormData, imageFile?: File | null): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form state
const loading = ref(false)
const imageUploading = ref(false)
const selectedImageFile = ref<File | null>(null)

const form = reactive<EventFormData>({
  title: '',
  description: '',
  startDate: '',
  endDate: '',
  startTime: '',
  endTime: '',
  location: '',
  category: '',
  isActive: true,
  maxAttendees: undefined,
  price: undefined,
  imageUrl: ''
})

// Initialize form with event data if editing
watchEffect(() => {
  if (props.event) {
    Object.assign(form, {
      title: props.event.title,
      description: props.event.description,
      startDate: props.event.startDate,
      endDate: props.event.endDate,
      startTime: props.event.startTime,
      endTime: props.event.endTime,
      location: props.event.location || '',
      category: props.event.category || '',
      isActive: props.event.isActive,
      maxAttendees: props.event.maxAttendees,
      price: props.event.price,
      imageUrl: props.event.imageUrl || ''
    })
  } else {
    // Reset form to defaults when creating new event
    Object.assign(form, {
      title: '',
      description: '',
      startDate: '',
      endDate: '',
      startTime: '',
      endTime: '',
      location: '',
      category: '',
      isActive: true,
      maxAttendees: undefined,
      price: undefined,
      imageUrl: ''
    })
  }
})

// Handle image upload
const handleImageUpload = async (file: File) => {
  selectedImageFile.value = file
  // Store the file for later use when saving the event
  // The actual upload will happen when the form is submitted
}

// Form submission
const handleSubmit = async () => {
  loading.value = true

  try {
    // Pass both form data and image file to parent
    emit('save', { ...form }, selectedImageFile.value)
  } catch (error) {
    console.error('Error submitting form:', error)
  } finally {
    loading.value = false
  }
}
</script>
