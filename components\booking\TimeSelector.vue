<template>
  <div class="space-y-4">
    <!-- Date Selection -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-neutral-700">
        Select Date <span class="text-red-500">*</span>
      </label>
      <input
        v-model="selectedDate"
        type="date"
        :min="minDate"
        :max="maxDate"
        required
        class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        @change="onDateChange"
      />
    </div>

    <!-- Time Slots -->
    <div v-if="selectedDate" class="space-y-2">
      <label class="block text-sm font-medium text-neutral-700">
        Available Time Slots <span class="text-red-500">*</span>
      </label>
      
      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-8">
        <Icon name="lucide:loader-2" class="w-6 h-6 animate-spin text-primary-500" />
        <span class="ml-2 text-sm text-neutral-600">Loading available times...</span>
      </div>
      
      <!-- No Available Times -->
      <div v-else-if="availableTimeSlots.length === 0" class="text-center py-8">
        <Icon name="lucide:clock-x" class="w-12 h-12 text-neutral-400 mx-auto mb-2" />
        <p class="text-sm text-neutral-600">No available time slots for this date</p>
        <p class="text-xs text-neutral-500 mt-1">Please select a different date</p>
      </div>
      
      <!-- Time Slots Grid -->
      <div v-else class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
        <button
          v-for="timeSlot in availableTimeSlots"
          :key="timeSlot.time"
          type="button"
          :class="timeSlotClasses(timeSlot)"
          :disabled="!timeSlot.isAvailable"
          @click="selectTimeSlot(timeSlot)"
        >
          <div class="flex flex-col items-center">
            <span class="text-sm font-medium">{{ formatTime(timeSlot.time) }}</span>
            <span v-if="!timeSlot.isAvailable" class="text-xs text-neutral-500">Booked</span>
          </div>
        </button>
      </div>
    </div>

    <!-- Selected Time Summary -->
    <div v-if="selectedTimeSlot" class="p-4 bg-primary-50 border border-primary-200 rounded-lg">
      <div class="flex items-center space-x-2">
        <Icon name="lucide:check-circle" class="w-5 h-5 text-primary-600" />
        <div>
          <p class="text-sm font-medium text-primary-900">
            Selected: {{ formatDate(selectedDate) }} at {{ formatTime(selectedTimeSlot.time) }}
          </p>
          <p class="text-xs text-primary-700">
            Duration: {{ selectedTimeSlot.duration }} minutes
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { BookingTimeSlot } from '~/types/service.types'

interface Props {
  serviceId: string
  serviceDuration: number
  modelValue?: BookingTimeSlot | null
  minDaysAhead?: number
  maxDaysAhead?: number
}

interface Emits {
  (e: 'update:modelValue', value: BookingTimeSlot | null): void
  (e: 'timeSelected', value: BookingTimeSlot): void
}

const props = withDefaults(defineProps<Props>(), {
  minDaysAhead: 0,
  maxDaysAhead: 30
})

const emit = defineEmits<Emits>()

// Reactive state
const selectedDate = ref('')
const selectedTimeSlot = ref<BookingTimeSlot | null>(null)
const availableTimeSlots = ref<BookingTimeSlot[]>([])
const loading = ref(false)

// Computed properties
const minDate = computed(() => {
  const date = new Date()
  date.setDate(date.getDate() + props.minDaysAhead)
  return date.toISOString().split('T')[0]
})

const maxDate = computed(() => {
  const date = new Date()
  date.setDate(date.getDate() + props.maxDaysAhead)
  return date.toISOString().split('T')[0]
})

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    selectedDate.value = newValue.date
    selectedTimeSlot.value = newValue
  } else {
    selectedDate.value = ''
    selectedTimeSlot.value = null
  }
}, { immediate: true })

// Methods
const onDateChange = async () => {
  selectedTimeSlot.value = null
  emit('update:modelValue', null)
  
  if (selectedDate.value) {
    await loadAvailableTimeSlots()
  }
}

const loadAvailableTimeSlots = async () => {
  loading.value = true
  
  try {
    // Mock implementation - replace with actual API call
    const mockTimeSlots: BookingTimeSlot[] = generateTimeSlots(selectedDate.value)
    availableTimeSlots.value = mockTimeSlots
  } catch (error) {
    console.error('Error loading time slots:', error)
    availableTimeSlots.value = []
  } finally {
    loading.value = false
  }
}

const generateTimeSlots = (date: string): BookingTimeSlot[] => {
  const slots: BookingTimeSlot[] = []
  const startHour = 9 // 9 AM
  const endHour = 17 // 5 PM
  const slotInterval = 30 // 30 minutes
  
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += slotInterval) {
      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      
      // Mock availability - in real implementation, check against existing bookings
      const isAvailable = Math.random() > 0.3 // 70% chance of being available
      
      slots.push({
        date,
        time,
        duration: props.serviceDuration,
        isAvailable
      })
    }
  }
  
  return slots
}

const selectTimeSlot = (timeSlot: BookingTimeSlot) => {
  if (!timeSlot.isAvailable) return
  
  selectedTimeSlot.value = timeSlot
  emit('update:modelValue', timeSlot)
  emit('timeSelected', timeSlot)
}

const timeSlotClasses = (timeSlot: BookingTimeSlot) => {
  const baseClasses = 'p-3 rounded-lg border transition-all duration-200 text-center'
  
  if (!timeSlot.isAvailable) {
    return `${baseClasses} bg-neutral-100 border-neutral-200 text-neutral-400 cursor-not-allowed`
  }
  
  if (selectedTimeSlot.value?.time === timeSlot.time) {
    return `${baseClasses} bg-primary-600 border-primary-600 text-white shadow-md`
  }
  
  return `${baseClasses} bg-white border-neutral-300 text-neutral-700 hover:border-primary-500 hover:bg-primary-50 cursor-pointer`
}

// Helper functions
const formatTime = (time: string): string => {
  const [hours, minutes] = time.split(':')
  const hour = parseInt(hours)
  const ampm = hour >= 12 ? 'PM' : 'AM'
  const displayHour = hour % 12 || 12
  return `${displayHour}:${minutes} ${ampm}`
}

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>
